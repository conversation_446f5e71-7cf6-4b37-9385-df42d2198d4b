#!/usr/bin/env python3
"""
Email sanitizer module - removes attachments and keeps only text content.
Extracted from convertMboxToEml.py for better code organization.
"""

import re
from typing import List, Tuple
from io import BytesIO
from collections import defaultdict

# Email stdlib
from email import policy
from email.generator import BytesGenerator
from email.message import EmailMessage, Message
from email.parser import BytesParser

# --- HTML cleaning regexes ---
CID_IMG_RE = re.compile(r'<img\b[^>]*\bsrc=["\']cid:[^"\']+["\'][^>]*>', re.I)
DATA_IMG_RE = re.compile(r'<img\b[^>]*\bsrc=["\']data:image/[^"\']+["\'][^>]*>', re.I)

def clean_html(html: str) -> str:
    """Remove inline images from HTML content."""
    html = CID_IMG_RE.sub('', html)
    html = DATA_IMG_RE.sub('', html)
    return html

# --- Header handling sets ---
ADDRESS_HEADERS = {
    'to', 'cc', 'bcc', 'from', 'reply-to', 'sender',
    'resent-to', 'resent-cc', 'resent-bcc', 'resent-from', 'resent-sender',
}

UNIQUE_KEEP_FIRST = {
    'subject', 'date', 'message-id', 'in-reply-to', 'references',
    'mime-version', 'return-path', 'resent-date', 'resent-message-id',
}

MULTI_ALLOWED = {
    'received', 'authentication-results', 'dkim-signature',
    'arc-seal', 'arc-message-signature', 'arc-authentication-results',
    'comments', 'keywords',
}

def canonical_header_name(name: str) -> str:
    """Convert header name to proper case."""
    return "-".join(part.capitalize() for part in name.split("-"))

def clean_header_value(val: str) -> str:
    """Remove illegal CR/LF, collapse whitespace, strip."""
    if not isinstance(val, str):
        try:
            val = str(val)
        except Exception:
            return ""
    val = val.replace("\r", " ").replace("\n", " ")
    val = re.sub(r"[ \t]+", " ", val)
    return val.strip()

def copy_non_content_headers(src_msg, dst_msg):
    """Copy all headers except Content-* headers with proper deduplication."""
    addr_acc = defaultdict(list)
    seen_unique = set()

    for k, v in src_msg.raw_items():
        kl = k.lower()
        if kl.startswith("content-"):
            continue

        v = clean_header_value(v)
        if not v:
            continue

        if kl in ADDRESS_HEADERS:
            addr_acc[kl].append(v)
            continue

        if kl in MULTI_ALLOWED:
            dst_msg[canonical_header_name(kl)] = v
            continue

        if kl in UNIQUE_KEEP_FIRST:
            if kl not in seen_unique:
                dst_msg[canonical_header_name(kl)] = v
                seen_unique.add(kl)
            continue

        # default: keep only first occurrence
        if kl not in seen_unique:
            dst_msg[canonical_header_name(kl)] = v
            seen_unique.add(kl)

    # merge address headers
    for kl, vals in addr_acc.items():
        if not vals:
            continue
        name = canonical_header_name(kl)
        combined = ", ".join(vals)
        combined = clean_header_value(combined)
        if combined:
            dst_msg[name] = combined

def extract_text_bodies(msg: Message, preserve_nested_text: bool) -> Tuple[List[str], List[str]]:
    """Extract text/plain and text/html bodies from email message."""
    plain: List[str] = []
    html: List[str] = []

    def walk(m: Message):
        ctype = (m.get_content_type() or '').lower()
        if m.is_multipart():
            for p in m.iter_parts():
                walk(p)
            return

        if ctype == 'text/plain':
            try:
                plain.append(m.get_content())
            except Exception:
                payload = m.get_payload(decode=True) or b''
                plain.append(payload.decode(m.get_content_charset() or 'utf-8', 'replace'))
        elif ctype == 'text/html':
            try:
                h = m.get_content()
            except Exception:
                payload = m.get_payload(decode=True) or b''
                h = payload.decode(m.get_content_charset() or 'utf-8', 'replace')
            html.append(clean_html(h))
        elif ctype.startswith('message/') and preserve_nested_text:
            payload = m.get_payload()
            nested = []
            if isinstance(payload, list):
                nested = [x for x in payload if isinstance(x, Message)]
            elif isinstance(payload, Message):
                nested = [payload]
            for sub in nested:
                p2, h2 = extract_text_bodies(sub, preserve_nested_text=True)
                if p2:
                    plain.append('\n\n----- Embedded message -----\n' + '\n\n'.join(p2))
                if h2:
                    html.append('<hr><blockquote>' + '\n\n'.join(h2) + '</blockquote>')

    walk(msg)
    return plain, html

def build_text_only(msg: Message, preserve_nested_text: bool) -> EmailMessage:
    """Build a new email message with only text content."""
    p_bodies, h_bodies = extract_text_bodies(msg, preserve_nested_text)

    out = EmailMessage()
    copy_non_content_headers(msg, out)

    plain = "\n\n".join(p_bodies) if p_bodies else ""
    html = "\n\n".join(h_bodies) if h_bodies else ""

    if plain and html:
        out.set_content(plain)
        out.add_alternative(html, subtype="html")
    elif html:
        out.set_content(html, subtype="html")
    else:
        out.set_content(plain)

    def strip_disposition(m: EmailMessage):
        if m.is_multipart():
            for p in m.iter_parts():
                strip_disposition(p)
        else:
            if "Content-Disposition" in m:
                del m["Content-Disposition"]
    strip_disposition(out)

    return out

def sanitize_eml_bytes(raw_eml_bytes: bytes, preserve_nested_text: bool = False) -> bytes:
    """Main function to sanitize email bytes - removes all attachments."""
    msg = BytesParser(policy=policy.default).parsebytes(raw_eml_bytes)
    cleaned = build_text_only(msg, preserve_nested_text=preserve_nested_text)
    buf = BytesIO()
    BytesGenerator(buf, policy=policy.default.clone(max_line_length=998)).flatten(cleaned)
    return buf.getvalue()