#!/usr/bin/env python3
"""
mbox_to_eml_sanitize_upload.py

- Ingests an mbox
- Converts each message to .eml
- (Optionally) Removes ALL attachments & inline images (whitelist: text/plain + text/html)
- Optionally gzip before S3 upload
- Uploads to S3 (and/or saves locally)

All boolean CLI args require explicit true/false.
"""

import os
import re
import sys
import gzip
import csv
import hashlib
import argparse
import mailbox
import boto3
import time
from typing import Tuple
from io import BytesIO
from concurrent.futures import ThreadPoolExecutor, as_completed

# Email stdlib
from email import policy
from email.generator import BytesGenerator
from email.message import Message

from remove_attachments import sanitize_eml_bytes

# ---------------------- Filename & S3 helpers -------------------------------

SAFE_RE = re.compile(r'[^A-Za-z0-9_.-]+')

def safe_token(s: str, maxlen: int = 150) -> str:
    return SAFE_RE.sub('_', s)[:maxlen] if s else "msg"

def sha1_hex(b: bytes) -> str:
    return hashlib.sha1(b).hexdigest()

def raw_bytes_from_message(msg: Message) -> bytes:
    """Serialize a Message to raw .eml bytes using stdlib generator."""
    buf = BytesIO()
    BytesGenerator(buf, policy=policy.default.clone(max_line_length=998)).flatten(msg)
    return buf.getvalue()

def derive_filename_from_headers(msg: Message, index: int) -> str:
    """
    Prefer Message-ID (sanitized), include compact Date token if present,
    and always include a zero-padded index.
    """
    mid = (msg.get('Message-Id') or msg.get('Message-ID') or "").strip()
    mid = mid.replace('<', '').replace('>', '').replace('@', '_at_')

    date = (msg.get('Date') or "").strip()
    date_token = re.sub(r'[^A-Za-z0-9]', '', date)[:16] if date else ""

    core = safe_token(mid) if mid else sha1_hex(raw_bytes_from_message(msg))
    parts = [str(index).zfill(7)]
    if date_token:
        parts.append(safe_token(date_token))
    parts.append(core)
    return "__".join(parts) + ".eml"

def s3_key_for(prefix: str, user_id: str, filename: str) -> str:
    # raw/email/google-takeout/<user_id>/eml/<filename>
    return f"{prefix.rstrip('/')}/{user_id}/eml-python-test/{filename}"

# ------------------------------ CLI utils -----------------------------------

def str2bool(v: str) -> bool:
    if isinstance(v, bool):
        return v
    val = str(v).strip().lower()
    if val in ('1', 'true', 't', 'yes', 'y'):
        return True
    if val in ('0', 'false', 'f', 'no', 'n'):
        return False
    raise argparse.ArgumentTypeError("Boolean value expected (true/false)")

# ------------------------------ Core worker ---------------------------------

def process_one_message(
    idx: int,
    msg: Message,
    args,
    s3_client
) -> Tuple[str, str, str]:
    start = time.time()
    try:
        raw_bytes = raw_bytes_from_message(msg)
        filename = derive_filename_from_headers(msg, idx)

        # remove attachments (optional)
        if args.remove_attachments:
            before = len(raw_bytes)
            cleaned_bytes = sanitize_eml_bytes(
                raw_eml_bytes=raw_bytes,
                preserve_nested_text=args.preserve_nested_text
            )
            after = len(cleaned_bytes)
            reduction = before - after
            reduction_pct = (reduction / before * 100) if before else 0
            print(f"[{idx}] {filename}: attachments removed, size {before} → {after} ({reduction_pct:.1f}% smaller)")
        else:
            cleaned_bytes = raw_bytes
            print(f"[{idx}] {filename}: attachments kept (size {len(raw_bytes)})")

        # gzip if enabled
        body_bytes = cleaned_bytes
        content_encoding = None
        if args.gzip:
            body_bytes = gzip.compress(cleaned_bytes)
            content_encoding = "gzip"
            print(f"[{idx}] {filename}: gzipped to {len(body_bytes)} bytes")

        # save local
        if args.save_local:
            out_dir = os.path.join(os.path.dirname(args.mbox), args.user_id, "eml")
            os.makedirs(out_dir, exist_ok=True)
            local_path = os.path.join(out_dir, filename)
            with open(local_path, "wb") as f:
                f.write(cleaned_bytes)
            print(f"[{idx}] {filename}: saved locally → {local_path}")

        # upload S3
        if args.save_s3:
            key = s3_key_for(args.s3_prefix, args.user_id, filename)
            s3_client.put_object(
                Bucket=args.bucket,
                Key=key,
                Body=body_bytes,
                ContentType="message/rfc822",
                **({"ContentEncoding": content_encoding} if content_encoding else {})
            )
            print(f"[{idx}] {filename}: uploaded to s3://{args.bucket}/{key}")

        elapsed = time.time() - start
        return ("UPLOADED", filename, f"{elapsed:.2f}s")

    except Exception as e:
        print(f"[{idx}] ERROR {filename if 'filename' in locals() else ''}: {e}", file=sys.stderr)
        return ("ERROR", f"#{idx}", repr(e))

# ---------------------------------- Main ------------------------------------

def main():
    ap = argparse.ArgumentParser(description="Convert mbox -> EML, optional attachment removal, upload to S3")
    ap.add_argument("--user-id", required=True, help="User identifier (for S3 key prefix)")
    ap.add_argument("--mbox", required=True, help="Path to the mbox file")
    ap.add_argument("--bucket", default="hle-ai-datalake")
    ap.add_argument("--region", default="ap-southeast-2")
    ap.add_argument("--s3-prefix", default="raw/email/google-takeout", help="S3 prefix before <user>/eml/<file>")

    # All booleans require explicit true/false
    ap.add_argument("--save-s3", type=str2bool, default=True, help="Upload .eml to S3 (true/false)")
    ap.add_argument("--save-local", type=str2bool, default=False, help="Save .eml locally under <mbox_dir>/<user>/eml/ (true/false)")
    ap.add_argument("--remove-attachments", type=str2bool, default=True, help="Strip attachments/inline images (true/false)")
    ap.add_argument("--preserve-nested-text", type=str2bool, default=False, help="Inline readable text from embedded message/rfc822 parts (true/false)")
    ap.add_argument("--gzip", type=str2bool, default=False, help="Gzip EML body before S3 upload (true/false)")
    ap.add_argument("--accelerate", type=str2bool, default=False, help="Use S3 Transfer Acceleration (true/false)")
    ap.add_argument("--max", type=int, default=None, help="Limit number of messages processed")
    ap.add_argument("--concurrency", type=int, default=16, help="Parallelism for processing/uploads")
    ap.add_argument("--log-csv", default="mbox_sanitize_log.csv", help="Path to CSV log")
    args = ap.parse_args()

    if not os.path.exists(args.mbox):
        print(f"ERROR: mbox not found: {args.mbox}", file=sys.stderr)
        sys.exit(1)

    # Boto3 S3 client
    s3_config = {}
    if args.accelerate:
        from boto3.session import Config
        s3_config["config"] = Config(s3={"use_accelerate_endpoint": True})
    s3_client = boto3.client("s3", region_name=args.region, **s3_config)

    # Prepare logging
    log_rows = []
    processed = 0
    ok = 0
    errors = 0

    # Open mbox and iterate messages
    mbox = mailbox.mbox(args.mbox, factory=None, create=False)

    # Thread pool for parallel processing (I/O heavy: S3 + file writes)
    with ThreadPoolExecutor(max_workers=args.concurrency) as ex:
        futures = {}
        for idx, msg in enumerate(mbox):
            if args.max is not None and processed >= args.max:
                break
            processed += 1
            futures[ex.submit(process_one_message, idx, msg, args, s3_client)] = idx

        for fut in as_completed(futures):
            status, name_or_index, err = fut.result()
            if status == "UPLOADED":
                ok += 1
                log_rows.append([status, name_or_index, ""])
                print(f"UPLOADED: {name_or_index}")
            else:
                errors += 1
                log_rows.append([status, name_or_index, err])
                print(f"ERROR on {name_or_index}: {err}", file=sys.stderr)

    # Write CSV log
    with open(args.log_csv, "w", newline="", encoding="utf-8") as f:
        w = csv.writer(f)
        w.writerow(["status", "filename_or_index", "error"])
        w.writerows(log_rows)

    print(f"Done. processed={processed} ok={ok} errors={errors}")
    if errors:
        sys.exit(2)

if __name__ == "__main__":
    main()
