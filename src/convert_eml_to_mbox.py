#!/usr/bin/env python3

"""
Unified CLI for EML to MBOX conversion:
1) s3-to-s3       : EMLs from S3 prefix → MBOX to S3 object
2) local-to-local : EMLs from local dir → MBOX to local file
3) s3-to-local    : EMLs from S3 prefix → MBOX to local file
"""

import os
import sys
import re
import argparse
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Tuple, NamedTuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import email.parser
import email.utils
from io import BytesIO

try:
    import boto3
    from botocore.exceptions import ClientError
except ImportError:
    print("Please install boto3: pip install boto3")
    sys.exit(1)


class EmailMeta(NamedTuple):
    """Metadata for an email message"""
    id: str  # local file path or S3 key
    date: datetime
    from_addr: str
    size: Optional[int] = None


# ---------- <PERSON><PERSON> Helpers ----------

def build_mbox_from_line(from_addr: str, date: datetime) -> str:
    """Build the 'From ' line for MBOX format"""
    # Format: From <EMAIL> Thu Oct 24 10:30:00 2024
    ctime_like = date.strftime("%a %b %d %H:%M:%S %Y")
    return f"From {from_addr} {ctime_like}"


def mbox_escape_message(content: bytes) -> bytes:
    """
    Escape body lines beginning with "From " according to mboxrd format.
    Adds leading '>' to lines starting with one or more '>' followed by 'From '.
    """
    lines = content.split(b'\n')
    result = []
    in_headers = True
    
    for line in lines:
        if in_headers:
            if line == b'':
                in_headers = False
            result.append(line)
        else:
            # In body: escape lines starting with '>*From '
            if re.match(rb'^>*From ', line):
                result.append(b'>' + line)
            else:
                result.append(line)
    
    return b'\n'.join(result)


# ---------- Header Parsing ----------

def parse_headers_from_buffer(buf: bytes) -> Dict[str, any]:
    """Parse email headers from a buffer"""
    # Find header/body boundary
    end = -1
    
    # Check for CRLF CRLF
    idx = buf.find(b'\r\n\r\n')
    if idx != -1:
        end = idx + 4
    else:
        # Check for LF LF
        idx = buf.find(b'\n\n')
        if idx != -1:
            end = idx + 2
        else:
            # Check for LF CR LF
            idx = buf.find(b'\n\r\n')
            if idx != -1:
                end = idx + 3
    
    header_bytes = buf[:end] if end > 0 else buf
    
    # Parse headers
    parser = email.parser.BytesParser()
    msg = parser.parsebytes(header_bytes, headersonly=True)
    
    # Extract Date
    date_raw = msg.get('Date', '')
    date = parse_date_or_epoch(date_raw)
    
    # Extract From
    from_raw = msg.get('From', '')
    from_addr = extract_addr(from_raw)
    
    return {'date': date, 'from': from_addr}


def extract_addr(from_raw: str) -> str:
    """Extract email address from From header"""
    if not from_raw:
        return "<EMAIL>"
    
    # Try to parse with email.utils
    name, addr = email.utils.parseaddr(from_raw)
    if addr:
        return addr
    
    # Fallback to regex
    match = re.search(r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}', from_raw)
    return match.group(0) if match else "<EMAIL>"


def parse_date_or_epoch(date_raw: str) -> datetime:
    """Parse date string or return epoch (always returns timezone-aware datetime)"""
    from datetime import timezone
    
    if not date_raw:
        # Return epoch as timezone-aware (UTC)
        return datetime.fromtimestamp(0, tz=timezone.utc)
    
    try:
        # Use email.utils.parsedate_to_datetime (returns timezone-aware)
        dt = email.utils.parsedate_to_datetime(date_raw)
        # If somehow naive, make it aware (UTC)
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return dt
    except:
        try:
            # Fallback to basic parsing
            dt = datetime.fromisoformat(date_raw)
            # Make timezone-aware if naive
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt
        except:
            # Return epoch as timezone-aware (UTC)
            return datetime.fromtimestamp(0, tz=timezone.utc)


# ---------- Local File Operations ----------

def read_local_headers(file_path: str, max_scan: int = 256 * 1024) -> Dict[str, any]:
    """Read and parse headers from a local EML file"""
    with open(file_path, 'rb') as f:
        buf = f.read(max_scan)
        return parse_headers_from_buffer(buf)


def stream_local_eml_escaped(file_path: str) -> bytes:
    """Read and escape a local EML file for MBOX format"""
    with open(file_path, 'rb') as f:
        content = f.read()
    return mbox_escape_message(content)


# ---------- S3 Operations ----------

def list_s3_keys(s3_client, bucket: str, prefix: str, limit: Optional[int] = None, 
                  suffix: Optional[str] = None) -> List[Dict]:
    """List S3 objects with optional suffix filter"""
    paginator = s3_client.get_paginator('list_objects_v2')
    page_iterator = paginator.paginate(Bucket=bucket, Prefix=prefix)
    
    objects = []
    for page in page_iterator:
        if 'Contents' in page:
            items = page['Contents']
            if suffix:
                items = [o for o in items if o['Key'].lower().endswith(suffix.lower())]
            objects.extend(items)
            if limit and len(objects) >= limit:
                return objects[:limit]
    
    return objects


def read_s3_headers(s3_client, bucket: str, key: str, range_bytes: int = 256 * 1024) -> Dict[str, any]:
    """Read and parse headers from an S3 object"""
    try:
        response = s3_client.get_object(
            Bucket=bucket,
            Key=key,
            Range=f'bytes=0-{range_bytes-1}'
        )
        buf = response['Body'].read()
        return parse_headers_from_buffer(buf)
    except Exception as e:
        print(f"Warning: Failed to read headers from {key}: {e}")
        return {'date': datetime.fromtimestamp(0, tz=timezone.utc), 'from': '<EMAIL>'}


def stream_s3_eml_escaped(s3_client, bucket: str, key: str) -> bytes:
    """Read and escape an S3 EML object for MBOX format"""
    response = s3_client.get_object(Bucket=bucket, Key=key)
    content = response['Body'].read()
    return mbox_escape_message(content)


# ---------- Main Conversion Functions ----------

def write_local_mbox_from_local_dir(input_dir: str, output: str, sort_by_date: bool,
                                     concurrency: int, limit: Optional[int] = None,
                                     dry_run: bool = False):
    """Convert local EML files to local MBOX"""
    input_path = Path(input_dir)
    if not input_path.exists():
        raise FileNotFoundError(f"Input dir not found: {input_dir}")
    
    output_path = Path(output)
    if output_path.exists() and not dry_run:
        raise FileExistsError(f"Output exists: {output}")
    
    # Find all .eml files
    files = list(input_path.glob('*.eml')) + list(input_path.glob('*.EML'))
    if limit:
        files = files[:limit]
    
    if not files:
        raise ValueError("No .eml files found")
    
    print(f"Found {len(files)} .eml files")
    
    # Scan headers concurrently
    metas: List[EmailMeta] = []
    
    def scan_file(file_path):
        try:
            headers = read_local_headers(str(file_path))
            size = file_path.stat().st_size
            return EmailMeta(
                id=str(file_path),
                date=headers['date'],
                from_addr=headers['from'],
                size=size
            )
        except Exception as e:
            print(f"Warning: Failed to scan {file_path}: {e}")
            return EmailMeta(
                id=str(file_path),
                date=datetime.fromtimestamp(0, tz=timezone.utc),
                from_addr='<EMAIL>'
            )
    
    with ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = [executor.submit(scan_file, f) for f in files]
        for i, future in enumerate(as_completed(futures)):
            metas.append(future.result())
            if (i + 1) % 500 == 0:
                print(f"Scanned {i + 1}/{len(files)} headers...")
    
    if dry_run:
        print(f"DRY-RUN: scanned {len(metas)} files. Example: {metas[0] if metas else None}")
        return
    
    # Sort if requested
    if sort_by_date:
        metas.sort(key=lambda m: (m.date, m.id))
    
    # Write MBOX file
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output, 'wb') as out:
        written = 0
        for meta in metas:
            # Write From line
            from_line = build_mbox_from_line(meta.from_addr, meta.date)
            out.write(from_line.encode('utf-8') + b'\n')
            
            # Write escaped message
            escaped_content = stream_local_eml_escaped(meta.id)
            out.write(escaped_content)
            
            # Ensure message ends with newline
            if not escaped_content.endswith(b'\n'):
                out.write(b'\n')
            
            # Add blank line between messages
            out.write(b'\n')
            
            written += 1
            if written % 500 == 0:
                print(f"Wrote {written}/{len(metas)} messages...")
    
    print(f"Done. Wrote {len(metas)} messages to {output}")


def write_local_mbox_from_s3(s3_client, bucket: str, prefix: str, output: str,
                              sort_by_date: bool, concurrency: int, limit: Optional[int] = None,
                              dry_run: bool = False, suffix: Optional[str] = None):
    """Convert S3 EML files to local MBOX"""
    output_path = Path(output)
    if output_path.exists() and not dry_run:
        raise FileExistsError(f"Output exists: {output}")
    
    # List S3 objects
    objects = list_s3_keys(s3_client, bucket, prefix, limit, suffix)
    print(f"Found {len(objects)} objects under s3://{bucket}/{prefix}")
    
    if not objects:
        raise ValueError("No objects to process. Check prefix and suffix.")
    
    # Scan headers concurrently
    metas: List[EmailMeta] = []
    
    def scan_object(obj):
        try:
            headers = read_s3_headers(s3_client, bucket, obj['Key'])
            return EmailMeta(
                id=obj['Key'],
                date=headers['date'],
                from_addr=headers['from'],
                size=obj.get('Size')
            )
        except Exception as e:
            print(f"Warning: Failed to scan {obj['Key']}: {e}")
            return EmailMeta(
                id=obj['Key'],
                date=datetime.fromtimestamp(0, tz=timezone.utc),
                from_addr='<EMAIL>',
                size=obj.get('Size')
            )
    
    with ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = [executor.submit(scan_object, o) for o in objects]
        for i, future in enumerate(as_completed(futures)):
            metas.append(future.result())
            if (i + 1) % 500 == 0:
                print(f"Scanned {i + 1}/{len(objects)} headers...")
    
    if dry_run:
        print(f"DRY-RUN: scanned {len(metas)} S3 objects. Example: {metas[0] if metas else None}")
        return
    
    # Sort if requested
    if sort_by_date:
        metas.sort(key=lambda m: (m.date, m.id))
    
    # Write MBOX file
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output, 'wb') as out:
        written = 0
        for meta in metas:
            # Write From line
            from_line = build_mbox_from_line(meta.from_addr, meta.date)
            out.write(from_line.encode('utf-8') + b'\n')
            
            # Write escaped message
            escaped_content = stream_s3_eml_escaped(s3_client, bucket, meta.id)
            out.write(escaped_content)
            
            # Ensure message ends with newline
            if not escaped_content.endswith(b'\n'):
                out.write(b'\n')
            
            # Add blank line between messages
            out.write(b'\n')
            
            written += 1
            if written % 500 == 0:
                print(f"Wrote {written}/{len(metas)} messages...")
    
    print(f"Done. Wrote {len(metas)} messages to {output}")


def write_s3_mbox_from_s3(s3_client, src_bucket: str, prefix: str, dest_bucket: str,
                          dest_key: str, sort_by_date: bool, concurrency: int,
                          limit: Optional[int] = None, dry_run: bool = False,
                          suffix: Optional[str] = None):
    """Convert S3 EML files to S3 MBOX"""
    # Check if destination exists
    try:
        s3_client.head_object(Bucket=dest_bucket, Key=dest_key)
        if not dry_run:
            print(f"Warning: Destination s3://{dest_bucket}/{dest_key} already exists")
    except:
        pass
    
    # List source objects
    objects = list_s3_keys(s3_client, src_bucket, prefix, limit, suffix)
    print(f"Found {len(objects)} objects under s3://{src_bucket}/{prefix}")
    
    if not objects:
        raise ValueError("No objects to process. Check prefix and suffix.")
    
    # Scan headers concurrently
    metas: List[EmailMeta] = []
    
    def scan_object(obj):
        try:
            headers = read_s3_headers(s3_client, src_bucket, obj['Key'])
            return EmailMeta(
                id=obj['Key'],
                date=headers['date'],
                from_addr=headers['from'],
                size=obj.get('Size')
            )
        except Exception as e:
            print(f"Warning: Failed to scan {obj['Key']}: {e}")
            return EmailMeta(
                id=obj['Key'],
                date=datetime.fromtimestamp(0, tz=timezone.utc),
                from_addr='<EMAIL>',
                size=obj.get('Size')
            )
    
    with ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = [executor.submit(scan_object, o) for o in objects]
        for i, future in enumerate(as_completed(futures)):
            metas.append(future.result())
            if (i + 1) % 500 == 0:
                print(f"Scanned {i + 1}/{len(objects)} headers...")
    
    if dry_run:
        print(f"DRY-RUN: scanned {len(metas)} S3 objects. Example: {metas[0] if metas else None}")
        return
    
    # Sort if requested
    if sort_by_date:
        metas.sort(key=lambda m: (m.date, m.id))
    
    print(f"Uploading to s3://{dest_bucket}/{dest_key}...")
    
    # Build MBOX content
    buffer = BytesIO()
    written = 0
    
    for meta in metas:
        # Write From line
        from_line = build_mbox_from_line(meta.from_addr, meta.date)
        buffer.write(from_line.encode('utf-8') + b'\n')
        
        # Write escaped message
        escaped_content = stream_s3_eml_escaped(s3_client, src_bucket, meta.id)
        buffer.write(escaped_content)
        
        # Ensure message ends with newline
        if not escaped_content.endswith(b'\n'):
            buffer.write(b'\n')
        
        # Add blank line between messages
        buffer.write(b'\n')
        
        written += 1
        if written % 500 == 0:
            print(f"Processed {written}/{len(metas)} messages...")
    
    # Upload to S3
    buffer.seek(0)
    s3_client.put_object(
        Bucket=dest_bucket,
        Key=dest_key,
        Body=buffer.getvalue(),
        ContentType='application/mbox'
    )
    
    print(f"Done. Wrote {len(metas)} messages to s3://{dest_bucket}/{dest_key}")


# ---------- Main ----------

def main():
    parser = argparse.ArgumentParser(description='Convert EML files to MBOX format')
    parser.add_argument('--mode', choices=['s3-to-s3', 'local-to-local', 's3-to-local'],
                        required=True, help='Conversion mode')
    
    # Common options
    parser.add_argument('--sort-by-date', action='store_true', default=False,
                        help='Sort messages by Date header')
    parser.add_argument('--header-concurrency', type=int, default=64,
                        help='Concurrent header scans')
    parser.add_argument('--limit', type=int, help='Process only first N objects/files')
    parser.add_argument('--dry-run', action='store_true', default=False,
                        help='List + header-scan only; no writes')
    parser.add_argument('--suffix', type=str, default='.eml',
                        help='Optional key/filename suffix filter. Set to empty string to disable.')
    
    # Local options
    parser.add_argument('--input-dir', type=str, help='Local directory of .eml files (local modes)')
    parser.add_argument('--output', type=str, help='Local output .mbox path (local output modes)')
    parser.add_argument('--overwrite', action='store_true', default=False,
                        help='Overwrite local output if exists')
    
    # S3 options
    parser.add_argument('--region', type=str, default='ap-southeast-2',
                        help='AWS region')
    parser.add_argument('--source-bucket', type=str, help='Source S3 bucket')
    parser.add_argument('--prefix', type=str, help='Source S3 prefix containing EMLs')
    parser.add_argument('--dest-bucket', type=str, help='Destination S3 bucket for MBOX')
    parser.add_argument('--dest-key', type=str, help='Destination S3 key for MBOX')
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'local-to-local':
            if not args.input_dir or not args.output:
                raise ValueError("local-to-local requires --input-dir and --output")
            
            if os.path.exists(args.output) and not args.overwrite:
                raise FileExistsError(f"Output exists: {args.output}. Use --overwrite to replace")
            
            write_local_mbox_from_local_dir(
                args.input_dir, args.output, args.sort_by_date,
                args.header_concurrency, args.limit, args.dry_run
            )
            print("Done (local → local)")
            
        else:
            # Initialize S3 client
            s3 = boto3.client('s3', region_name=args.region)
            
            if args.mode == 's3-to-local':
                if not args.source_bucket or not args.prefix or not args.output:
                    raise ValueError("s3-to-local requires --source-bucket, --prefix, --output")
                
                if os.path.exists(args.output) and not args.overwrite:
                    raise FileExistsError(f"Output exists: {args.output}. Use --overwrite to replace")
                
                write_local_mbox_from_s3(
                    s3, args.source_bucket, args.prefix, args.output,
                    args.sort_by_date, args.header_concurrency, args.limit,
                    args.dry_run, args.suffix if args.suffix else None
                )
                print("Done (S3 → local)")
                
            elif args.mode == 's3-to-s3':
                if not args.source_bucket or not args.prefix or not args.dest_bucket or not args.dest_key:
                    raise ValueError("s3-to-s3 requires --source-bucket, --prefix, --dest-bucket, --dest-key")
                
                write_s3_mbox_from_s3(
                    s3, args.source_bucket, args.prefix, args.dest_bucket, args.dest_key,
                    args.sort_by_date, args.header_concurrency, args.limit,
                    args.dry_run, args.suffix if args.suffix else None
                )
                print("Done (S3 → S3)")
                
    except Exception as e:
        print(f"Fatal: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()