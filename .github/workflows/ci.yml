name: CI

on:
  push:
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install boto3
      - name: Test import
        run: |
          python -c "from src.convert_mbox_to_eml import main; from src.remove_attachments import sanitize_eml_bytes"
