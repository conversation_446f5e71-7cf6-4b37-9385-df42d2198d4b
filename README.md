# mbox-to-eml-converter

Convert mbox files to individual EML files, remove attachments, and upload to S3.

## Usage

```bash
python src/convert_mbox_to_eml.py \
  --user-id your-user-id \
  --mbox /path/to/your.mbox \
  --bucket your-s3-bucket \
  --remove-attachments true \
  --save-s3 true
```

## Features

- Converts mbox to individual EML files
- Removes attachments (keeps only text/plain and text/html)
- Uploads sanitized emails to S3
- Parallel processing for performance
